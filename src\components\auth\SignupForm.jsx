import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { FiUser, FiMail, FiCreditCard } from "react-icons/fi";
import { signupUser } from "../../store/slices/SignupSlice";
import { FormField, TextInput, PasswordInput } from "../ui/FormComponents";
import { BasicCard } from "../ui/cards";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";

const SignupForm = ({ role }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error } = useSelector((state) => state.signup);

  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    cnic: "",
    passport: "",
    password: "",
    confirmPassword: "",
  });

  const [errors, setErrors] = useState({});
  const [apiError, setApiError] = useState(null);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    if (errors[name]) setErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const handlePhoneChange = (value) => {
    setFormData((prev) => ({ ...prev, phone: value }));
    if (errors.phone) setErrors((prev) => ({ ...prev, phone: "" }));
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.fullName.trim()) newErrors.fullName = "Username is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    if (!formData.phone) newErrors.phone = "Phone number is required";
    if (!formData.password) newErrors.password = "Password is required";
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    try {
      // Map form data to API expected field names
      const apiData = {
        username: formData.fullName,    // Full Name -> username
        email: formData.email,
        mobile: formData.phone,         // Phone Number -> mobile
        password: formData.password,
        user_type: role,               // Role -> user_type
        ...(formData.cnic && { cnic: formData.cnic }),
        ...(formData.passport && { passport: formData.passport })
      };

      await dispatch(signupUser(apiData)).unwrap();
      navigate("/verify-email");
    } catch (err) {
      console.error("Signup failed:", err);
      setApiError(err);
    }
  };

  return (
    <div className="w-full max-w-3xl mx-auto">
      <BasicCard className="border border-gray-200 dark:border-gray-700 shadow-sm bg-white dark:bg-gray-900 rounded-2xl overflow-hidden">
        {/* Header */}
        <div className="px-8 py-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Create your account
          </h2>
          <p className="text-gray-500 dark:text-gray-400 mt-1 text-sm">
            Join EduFair as a {role} and start your journey today.
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-8 space-y-6">
          {apiError && (
            <div className="bg-red-50 border border-red-200 dark:bg-red-900/30 dark:border-red-800 text-red-700 dark:text-red-300 rounded-lg p-3 text-sm">
              <div className="font-medium mb-2">
                {typeof apiError === 'string' ? apiError : apiError.message || apiError.detail || 'An error occurred during signup'}
              </div>
              {apiError.details?.validation_errors && (
                <ul className="list-disc list-inside space-y-1 text-xs">
                  {apiError.details.validation_errors.map((validationError, index) => (
                    <li key={index}>
                      {validationError.field.replace('body -> ', '')}: {validationError.message}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          )}

          {/* Username + Email */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FormField label="Username" error={errors.fullName} required>
              <TextInput
                name="fullName"
                placeholder="Enter your username"
                value={formData.fullName}
                onChange={handleChange}
                icon={FiUser}
                error={errors.fullName}
              />
            </FormField>

            <FormField label="Email Address" error={errors.email} required>
              <TextInput
                type="email"
                name="email"
                placeholder="Enter your email"
                value={formData.email}
                onChange={handleChange}
                icon={FiMail}
                error={errors.email}
              />
            </FormField>
          </div>

          {/* Phone */}
<FormField label="Phone Number" error={errors.phone} required>
  <div
    className={`relative flex items-center rounded-lg border shadow-sm ${
      errors.phone
        ? "border-red-400 dark:border-red-500"
        : "border-gray-300 dark:border-gray-600 focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500"
    } bg-gray-50 dark:bg-gray-800`}
  >
    {/* PhoneInput with hidden borders */}
    <PhoneInput
      country={"pk"}
      value={formData.phone}
      onChange={handlePhoneChange}
      inputClass="!w-full !h-[46px] !pl-12 !pr-4 !py-3 !text-sm !bg-transparent !border-0 focus:!ring-0 focus:!outline-none !text-gray-900 dark:!text-gray-100 !placeholder-gray-400 dark:!placeholder-gray-500"
      buttonClass="!absolute !left-3 !top-1/2 !-translate-y-1/2 !bg-transparent !border-0 focus:!outline-none"
      containerClass="w-full"
      dropdownClass="!bg-white dark:!bg-gray-900 !text-gray-900 dark:!text-gray-100 !rounded-lg !shadow-lg !border !border-gray-200 dark:!border-gray-700 !max-h-60 overflow-auto"
      searchClass="!bg-gray-50 dark:!bg-gray-800 !text-gray-900 dark:!text-gray-100 !border-gray-300 dark:!border-gray-600 !rounded-md !px-3 !py-2 !m-2"
      enableSearch={true}
      searchPlaceholder="Search countries..."
      specialLabel=""
    />
  </div>
</FormField>



          {/* CNIC / Passport */}
          <FormField label="CNIC / Passport" error={errors.cnic || errors.passport}>
            <TextInput
              placeholder="Enter your CNIC or Passport Number"
              value={formData.cnic || formData.passport}
              onChange={handleChange}
              name="cnic"
              icon={FiCreditCard}
              error={errors.cnic || errors.passport}
            />
          </FormField>

          {/* Passwords */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField label="Password" error={errors.password}>
              <PasswordInput
                name="password"
                placeholder="Create a strong password"
                value={formData.password}
                onChange={handleChange}
                error={errors.password}
              />
            </FormField>

            <FormField label="Confirm Password" error={errors.confirmPassword}>
              <PasswordInput
                name="confirmPassword"
                placeholder="Confirm your password"
                value={formData.confirmPassword}
                onChange={handleChange}
                error={errors.confirmPassword}
              />
            </FormField>
          </div>

          {/* Submit */}
          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white font-medium py-3 rounded-lg transition-colors duration-200"
          >
            {loading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Creating account...</span>
              </div>
            ) : (
              "Create account"
            )}
          </button>

          {/* Login redirect */}
          <p className="text-center text-sm text-gray-600 dark:text-gray-400">
            Already have an account?{" "}
            <button
              type="button"
              onClick={() => navigate("/login")}
              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
            >
              Sign in
            </button>
          </p>
        </form>
      </BasicCard>
    </div>
  );
};

export default SignupForm;
